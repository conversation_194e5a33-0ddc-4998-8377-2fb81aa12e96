{"$schema": "https://biomejs.dev/schemas/1.8.3/schema.json", "formatter": {"enabled": true, "formatWithErrors": true, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 120, "attributePosition": "auto", "ignore": ["src/assets/*"]}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": false, "a11y": {"noBlankTarget": "error"}, "complexity": {"noBannedTypes": "error", "noExtraBooleanCast": "error", "noMultipleSpacesInRegularExpressionLiterals": "error", "noUselessCatch": "error", "noUselessTypeConstraint": "error", "noWith": "error"}, "correctness": {"noChildrenProp": "error", "noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInnerDeclarations": "error", "noInvalidConstructorSuper": "error", "noNewSymbol": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedVariables": "error", "useArrayLiterals": "off", "useIsNan": "error", "useJsxKeyInIterable": "error", "useValidForDirection": "error", "useYield": "error"}, "security": {"noDangerouslySetInnerHtmlWithChildren": "error"}, "style": {"noNamespace": "error", "useAsConstAssertion": "error"}, "suspicious": {"noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCommentText": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateJsxProps": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "warn", "noExplicitAny": "warn", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "warn", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "useGetterReturn": "error", "useValidTypeof": "error"}}, "ignore": ["src/assets/*"]}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto"}}, "overrides": [{"include": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidConstructorSuper": "off", "noNewSymbol": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"noArguments": "error", "noVar": "error", "useConst": "error"}, "suspicious": {"noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "useGetterReturn": "off"}}}}]}