import { <PERSON><PERSON><PERSON>, <PERSON>Left } from "@mui/icons-material";
import { Box, Button, Grid, IconButton, Tooltip, Typography } from "@mui/material";
import React from "react";
import * as styles from "./styles";

interface ContentHeaderProps {
  title?: React.ReactNode;
  subtitle?: React.ReactNode;
  buttonTitle?: string;
  primaryAction?: (event: React.ChangeEvent<unknown>) => void;
  Icon?: React.ReactNode;
  secondaryButtonTitle?: string;
  secondaryIcon?: React.ReactNode;
  secondaryAction?: (event: React.ChangeEvent<unknown>) => void;
  allowAction?: boolean;
  actions?: React.ReactNode;
  showBackButton?: boolean;
  goBack?: () => void;
}

const ContentHeader: React.FC<ContentHeaderProps> = ({
  title,
  subtitle,
  buttonTitle,
  primaryAction,
  Icon,
  secondaryButtonTitle,
  secondaryIcon,
  secondaryAction,
  allowAction = true,
  actions = null,
  showBackButton = false,
  goBack,
}) => {
  return (
    <Box sx={styles.headerContainer}>
      <Box display="flex" alignItems={subtitle ? "flex-start" : "center"} gap={1}>
        {showBackButton && goBack && (
          <Tooltip title="Go Back">
            <IconButton onClick={goBack}>
              <ArrowBack color="secondary" />
            </IconButton>
          </Tooltip>
        )}
        <Box sx={styles.textContainer}>
          {title && (
            <Typography fontSize="18px" sx={styles.title}>
              {title}
            </Typography>
          )}
          {subtitle && <Typography sx={styles.subtitle}>{subtitle}</Typography>}
        </Box>
      </Box>
      <Grid container sx={{ justifyContent: "flex-end", width: "auto", gap: "16px" }}>
        <Grid item>
          {secondaryIcon
            ? secondaryIcon
            : secondaryButtonTitle && (
                <Button sx={styles.button} variant="text" onClick={secondaryAction}>
                  {secondaryButtonTitle}
                </Button>
              )}
        </Grid>
        {allowAction && (
          <Grid item>
            {Icon
              ? Icon
              : buttonTitle && (
                  <Button sx={styles.button} variant="contained" onClick={primaryAction}>
                    {buttonTitle}
                  </Button>
                )}
          </Grid>
        )}
        {actions && <Grid item>{actions}</Grid>}
      </Grid>
    </Box>
  );
};

export default ContentHeader;
