import { SxProps, Theme } from "@mui/material";

export const title = <SxProps<Theme>>{
  fontSize: 20,
  fontWeight: "bold",
};

export const subtitle = <SxProps<Theme>>{
  fontSize: 12,
  color: "#667085",
};

export const headerContainer = <SxProps<Theme>>{
  display: "flex",
  width: "100%",
  justifyContent: "space-between",
  alignItems: "center",
  margin: "0px 0px 0px 0px",
};

export const textContainer = <SxProps<Theme>>{
  display: "flex",
  flexDirection: "column",
  gap: 1,
};

export const button = <SxProps<Theme>>{
  borderRadius: 8,
  textTransform: "none",
  minWidth: "180px",
  padding: "0.75rem 1.25rem",
  fontSize: "1rem",
  fontWeight: "500",
  "&.MuiButton-contained": {
    "&:hover": {
      backgroundColor: "primary.light",
    },
    "&:disabled": {
      color: "primary.contrastText",
      backgroundColor: "primary.light",
      opacity: 0.5,
    },
  },
  "&.MuiButton-contained .MuiTouchRipple-child": {
    backgroundColor: "black",
  },
  "&.MuiButton-text": {
    border: "none",
    backgroundColor: "#EFF4F8",
    "&:hover": {
      backgroundColor: "#F2F3F3",
    },
    "&:disabled": {
      backgroundColor: "#EFF4F8",
    },
  },
};
