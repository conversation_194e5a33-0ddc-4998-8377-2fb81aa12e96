import { InputAdornment } from "@mui/material";
import React from "react";
import EffiTextField from "../EffiTextField";

type EffiCurrencyProps = {
  label: string;
  required?: boolean;
  size?: "small" | "medium";
  currency?: string;
  endHelperText?: string;
  [key: string]: any;
};

const getCurrencySymbol = (currencyCode: string): string => {
  try {
    const formatter = new Intl.NumberFormat("en", {
      style: "currency",
      currency: currencyCode,
      currencyDisplay: "narrowSymbol", // or 'symbol' for full
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

    // This will format something like "$100", "₹100", "£100"
    const parts = formatter.formatToParts(1);
    const symbolPart = parts.find((p) => p.type === "currency");
    return symbolPart?.value || currencyCode;
  } catch {
    return currencyCode; // fallback
  }
};

const EffiCurrency: React.FC<EffiCurrencyProps> = ({
  label,
  required,
  size = "small",
  currency = "INR",
  endHelperText = "",
  ...otherProps
}) => {
  return (
    <EffiTextField
      label={label}
      required={required}
      size={size}
      {...otherProps}
      type="number"
      min={0}
      max={100}
      slotProps={{
        input: {
          startAdornment: <InputAdornment position="start">{getCurrencySymbol(currency)}</InputAdornment>,
          endAdornment: <InputAdornment position="end">{endHelperText}</InputAdornment>,
        },
      }}
    />
  );
};

export default EffiCurrency;
