import { Add, Delete } from "@mui/icons-material";
import { Box, Button, Divider, Grid2, IconButton } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { DateArg, addDays, format } from "date-fns";
import React, { useMemo, useState, useImperativeHandle, useEffect } from "react";
import { useForm } from "src/customHooks/useForm";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { PayrollComponent, PayrollTemplate } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import validators from "src/utils/validators";
import { INPUT_FIELDS } from "../config/EmploymentDetails";
import { FormDataType, StepperComponentProps } from "../types/FormDataTypes";
import CompensationItem from "./CompensationItem";

export const buildRequestStructure = (
  templateName: string,
  effectiveDate: string | null,
  components: PayrollComponent[],
): any => {
  const buildComponents = (components: PayrollComponent[]): any[] => {
    return components.flatMap((component) => [
      {
        compensation_template_component: {
          name: component.name,
        },
        amount: component.amount || 0,
      },
      ...buildComponents(component.sub_components),
    ]);
  };

  return {
    compensation_template: {
      name: templateName, // Template name
    },
    components: buildComponents(components), // Flattened components including nested ones
    effective_date: effectiveDate ? format(effectiveDate, "yyyy-MM-dd") : null, // Hardcoded effective date
  };
};

type Props = StepperComponentProps & {
  formData?: {
    current_compensation: PayrollTemplate;
    next_compensation: PayrollTemplate;
  };
  disbaleFields?: string[];
  onFormComplete: (form: FormDataType[], isFormSubmit?: boolean, isSaveDraft?: boolean) => void;
  disabledInputFields?: string[][];
  disableDelete?: boolean[];
  initialFormState?: {
    current_compensation: PayrollTemplate;
    next_compensation: PayrollTemplate;
  };
  globalFormData: any;
};

type DefaultFormState = {
  template_name: string;
  effective_date: string;
};

const getTemplateDetails = (data: PayrollTemplate[], templateName: string) => {
  if (!data || data.length === 0) {
    return "";
  }
  if (!templateName) {
    return "";
  }
  return data.find((template) => template.name === templateName)?.name;
};

enum CompensationActionableStates {
  ADD_COMPENSATION = "add-next-compensation",
  DELETE_COMPENSATION = "delete-next-compensation",
}

const Compensation: React.FC<Props> = ({
  formActionButton,
  onFormComplete,
  formData,
  isViewOnlyMode,
  setDisableNext,
  initialFormState,
  globalFormData,
}) => {
  const jobTitleToFilter = useMemo(
    () => globalFormData?.employementDetails?.[0]?.[INPUT_FIELDS.JOB_TITLE_FORMATTED_NAME],
    [globalFormData?.employementDetails?.[0]?.[INPUT_FIELDS.JOB_TITLE_FORMATTED_NAME]],
  );

  const [componentsData, setComponentsData] = useState<PayrollComponent[]>([]);
  const [nextComponentsData, setNextComponentsData] = useState<PayrollComponent[]>([]);
  const [currentActionableState, setCurrentActionableState] = useState<CompensationActionableStates | null>(null);
  const isNextCompensationConfigurationAllowed = !!initialFormState?.current_compensation;

  const { data: templates } = useQuery({
    queryKey: ["get-all-employee-templates"],
    queryFn: async () => {
      const resp = await payrollService.getAllTemplates();
      return resp?.filter((eachTemplate) => eachTemplate?.job_titles?.includes(jobTitleToFilter));
    },
  });

  const defaultTemplateState = useMemo(
    () => ({
      template_name: getTemplateDetails(templates || [], formData?.current_compensation?.template_name || ""),
      effective_date:
        formData?.current_compensation?.effective_date || initialFormState?.current_compensation?.effective_from || "",
    }),
    [templates, formData],
  );

  const defaultNextTemplateState = useMemo(
    () => ({
      template_name: isNextCompensationConfigurationAllowed
        ? getTemplateDetails(
            templates || [],
            formData?.next_compensation?.template_name || initialFormState?.next_compensation?.template_name || "",
          )
        : null,
      effective_date:
        formData?.next_compensation?.effective_date || initialFormState?.next_compensation?.effective_from || "",
    }),
    [templates, formData, isNextCompensationConfigurationAllowed, currentActionableState],
  );

  const { formDetails, handleSelectChange, setFormDetail } = useForm({
    initialState: defaultTemplateState,
    validations: {
      template_name: [validators.validateSelectDropdown],
      effective_date: [],
    },
    isBulk: false,
  });

  const {
    formDetails: nextFormDetails,
    handleSelectChange: handleNextCompensationSelectChange,
    setFormDetail: selectNextCompensationFormDetail,
    areFormDetailsValid: areNextCompensationFormDetailsValid,
  } = useForm({
    initialState: defaultNextTemplateState,
    validations: {
      template_name: [validators.validateSelectDropdown],
      effective_date: [validators.validateInput],
    },
    isBulk: false,
  });

  const typedFormDetails = formDetails as unknown as DefaultFormState;

  const getRequestStructure = () => {
    // if current compensation is not present
    if (!initialFormState?.current_compensation) {
      return {
        current_compensation: {
          ...initialFormState?.current_compensation,
          ...formData?.current_compensation,
          template_name: typedFormDetails.template_name || formData?.current_compensation?.template_name,
          effective_date: typedFormDetails.effective_date || formData?.current_compensation?.effective_from,
          components: componentsData,
        },
        next_compensation: null,
      };
    }

    if (initialFormState?.current_compensation && !currentActionableState) {
      return {
        current_compensation: {
          ...initialFormState?.current_compensation,
          ...formData?.current_compensation,
          template_name: typedFormDetails.template_name || formData?.current_compensation?.template_name,
          effective_date: typedFormDetails.effective_date || formData?.current_compensation?.effective_from,
          components: componentsData,
        },
        next_compensation: null,
      };
    }

    if (
      initialFormState?.current_compensation &&
      currentActionableState === CompensationActionableStates.DELETE_COMPENSATION
    ) {
      return {
        current_compensation: {
          ...initialFormState?.current_compensation,
          ...formData?.current_compensation,
          template_name: typedFormDetails.template_name || formData?.current_compensation?.template_name,
          effective_date: typedFormDetails.effective_date || formData?.current_compensation?.effective_from,
          components: componentsData,
        },
        next_compensation: null,
      };
    }

    return {
      current_compensation: {
        ...(initialFormState?.current_compensation || {}),
        ...formData?.current_compensation,
        template_name: typedFormDetails.template_name || formData?.current_compensation?.template_name,
        effective_date: typedFormDetails.effective_date || formData?.current_compensation?.effective_from,
        components: componentsData,
      },
      next_compensation: {
        ...(initialFormState?.next_compensation || {}),
        ...formData?.next_compensation,
        template_name: nextFormDetails.template_name || formData?.next_compensation?.template_name,
        effective_date: nextFormDetails.effective_date || formData?.next_compensation?.effective_from,
        components: nextComponentsData,
      },
    };
  };

  useImperativeHandle(formActionButton, () => ({
    next: (isFormSubmit, isSaveDraft) => {
      const requestStructure = getRequestStructure();
      onFormComplete(requestStructure as unknown as FormDataType[], isFormSubmit, isSaveDraft);
    },
  }));

  const onAddMoreClick = () => {
    setCurrentActionableState(CompensationActionableStates.ADD_COMPENSATION);
  };

  const onDeleteClick = () => {
    setCurrentActionableState(CompensationActionableStates.DELETE_COMPENSATION);
  };

  const shouldNextCompensationBeVisible = useMemo(() => {
    if (currentActionableState === CompensationActionableStates.ADD_COMPENSATION) {
      return true;
    }

    if (!currentActionableState && formData?.next_compensation) {
      return true;
    }
    return false;
  }, [currentActionableState, initialFormState]);

  useEffect(() => {
    if (templates?.length === 0 && currentActionableState !== CompensationActionableStates.ADD_COMPENSATION) {
      setDisableNext?.(!initialFormState?.current_compensation?.template_name);
      return;
    }
    if (currentActionableState === CompensationActionableStates.ADD_COMPENSATION) {
      setDisableNext?.(!typedFormDetails?.template_name || !areNextCompensationFormDetailsValid);
      return;
    }
    setDisableNext?.(!typedFormDetails?.template_name);
  }, [
    componentsData,
    nextComponentsData,
    areNextCompensationFormDetailsValid,
    typedFormDetails,
    setDisableNext,
    currentActionableState,
    initialFormState,
    templates,
  ]);

  useEffect(() => {
    const currentState = formData?.next_compensation?.template_name
      ? CompensationActionableStates.ADD_COMPENSATION
      : null;
    setCurrentActionableState(currentState);
  }, [formData, initialFormState]);

  return (
    <Grid2 container spacing={2}>
      <Grid2 size={12}>
        <Box display="flex" flexDirection="column" gap={2}>
          <ContentHeader
            title={`Current Compensation (${typedFormDetails?.template_name || formData?.current_compensation?.template_name})`}
            subtitle={`This is the current applied compensation${formData?.current_compensation?.effective_from ? `, effective from ${format(formData?.current_compensation?.effective_from, "dd MMM yyyy") || ""}` : ""}`}
          />
          <Divider />
          <CompensationItem
            componentsData={componentsData}
            data={templates || []}
            formState={typedFormDetails}
            handleSelectChange={handleSelectChange}
            isViewOnlyMode={isViewOnlyMode}
            isCompensationAlreadyPresentForEmployee={
              (initialFormState?.current_compensation?.components?.length || 0) > 0
            }
            previousCompensationDetails={formData?.current_compensation}
            setComponentsData={setComponentsData}
            setFormDetail={setFormDetail}
            inputProps={{
              effective_date: {
                maxDate: new Date(),
                required: false,
              },
            }}
          />
        </Box>
      </Grid2>
      {!isViewOnlyMode && isNextCompensationConfigurationAllowed && (
        <Grid2
          size={2}
          visibility={
            !currentActionableState || currentActionableState === CompensationActionableStates.DELETE_COMPENSATION
              ? "visible"
              : "hidden"
          }
        >
          <Button
            variant="text"
            disabled={!isNextCompensationConfigurationAllowed}
            onClick={onAddMoreClick}
            startIcon={<Add fontSize="small" />}
            sx={{ width: 250, textAlign: "start" }}
          >
            Next Compensation
          </Button>
        </Grid2>
      )}
      {shouldNextCompensationBeVisible && (
        <Grid2 size={12}>
          <Box
            display="flex"
            flexDirection="column"
            gap={2}
            visibility={shouldNextCompensationBeVisible ? "visible" : "hidden"}
          >
            <ContentHeader
              title={`Next Compensation (${nextFormDetails?.template_name || formData?.next_compensation?.template_name || initialFormState?.next_compensation?.template_name || ""})`}
              subtitle={`This is the next applicable compensation${initialFormState?.next_compensation?.effective_from ? `, effective from ${format(initialFormState?.next_compensation?.effective_from, "dd MMM yyyy")}` : ""}`}
              Icon={
                !isViewOnlyMode && (
                  <IconButton onClick={onDeleteClick}>
                    <Delete color="error" />
                  </IconButton>
                )
              }
            />
            <Divider />
            <CompensationItem
              componentsData={nextComponentsData}
              data={templates || []}
              formState={nextFormDetails as unknown as DefaultFormState}
              isCompensationAlreadyPresentForEmployee={
                (initialFormState?.next_compensation?.components?.length || 0) > 0
              }
              handleSelectChange={handleNextCompensationSelectChange}
              isViewOnlyMode={isViewOnlyMode}
              previousCompensationDetails={formData?.next_compensation || initialFormState?.next_compensation}
              setComponentsData={setNextComponentsData}
              setFormDetail={selectNextCompensationFormDetail}
              inputProps={{
                effective_date: {
                  minDate: initialFormState?.current_compensation?.effective_from
                    ? addDays(initialFormState?.current_compensation?.effective_from as DateArg<Date>, 1)
                    : undefined,
                  required: true,
                },
              }}
            />
          </Box>
        </Grid2>
      )}
    </Grid2>
  );
};

export default Compensation;
