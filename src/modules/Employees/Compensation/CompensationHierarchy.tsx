import { Box, InputAdornment, Typography } from "@mui/material";
import React from "react";
import { CurrencyTextField } from "src/modules/Common/FormInputs/CustomTextField";
import { PayrollComponent } from "src/services/api_definitions/payroll.service";

type Props = {
  components: PayrollComponent[];
  handleAmountChange: (id: string, amount: number) => void;
  isViewOnlyMode?: boolean;
  isCompensationAlreadyPresentForEmployee: boolean;
};

const CompensationHierarchy: React.FC<Props> = ({
  components,
  handleAmountChange,
  isCompensationAlreadyPresentForEmployee,
  isViewOnlyMode = false,
}) => {
  return components.map((component) => (
    <Box key={component.id} sx={{ paddingLeft: 4, marginTop: 3 }}>
      <Typography variant="body1" sx={{ fontWeight: component.sub_components.length > 0 ? "bold" : "normal" }}>
        {component.name}
      </Typography>
      <CurrencyTextField
        type="number"
        size="small"
        showTooltip={false}
        value={component.amount || 0}
        onChange={(e) => handleAmountChange(component.id!, Number(e.target.value))}
        disabled={component.sub_components.length > 0 || isViewOnlyMode || isCompensationAlreadyPresentForEmployee}
        slotProps={{
          input: {
            startAdornment: <InputAdornment position="start">{component.currency}</InputAdornment>,
          },
        }}
      />
      {component.sub_components?.length > 0 && (
        <CompensationHierarchy
          components={component.sub_components}
          handleAmountChange={handleAmountChange}
          isViewOnlyMode={isViewOnlyMode}
          isCompensationAlreadyPresentForEmployee={isCompensationAlreadyPresentForEmployee}
        />
      )}
    </Box>
  ));
};

export default CompensationHierarchy;
