import { Box, Divider, Typography, styled } from "@mui/material";
import React, { useEffect, useMemo } from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import CustomDateField, { CustomDateFieldProps } from "src/modules/Common/FormInputs/CustomDateField";
import CustomSelect from "src/modules/Common/FormInputs/CustomSelect";
import EmployeeCompensation from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import { PayrollComponent, PayrollTemplate } from "src/services/api_definitions/payroll.service";
import CompensationHierarchy from "./CompensationHierarchy";

const TotalCTCContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  padding: theme.spacing(2),
  border: "1px solid #ddd",
  borderRadius: "8px",
  backgroundColor: "#f9f9f9",
  position: "sticky",
  bottom: 0,
}));

const injectIds = (components: PayrollComponent[], parentId: string = ""): PayrollComponent[] => {
  return components.map((component, index) => {
    const currentId = `${parentId}${parentId ? "-" : ""}${index}`;
    return {
      ...component,
      id: currentId,
      sub_components: injectIds(component.sub_components, currentId),
    };
  });
};

const CompensationItem = ({
  data,
  formState,
  setFormDetail,
  previousCompensationDetails,
  isViewOnlyMode = false,
  handleSelectChange,
  setComponentsData,
  componentsData,
  isCompensationAlreadyPresentForEmployee = false,
  hiddenElements = {
    effective_date: false,
  },
  inputProps = {
    effective_date: {},
  },
}: {
  data: PayrollTemplate[];
  formState: {
    template_name: string;
    effective_date: string;
  };
  setFormDetail: (key: string, value: any) => void;
  componentsData: PayrollComponent[];
  setComponentsData: React.Dispatch<React.SetStateAction<PayrollComponent[]>>;
  isViewOnlyMode?: boolean;
  isCompensationAlreadyPresentForEmployee?: boolean;
  handleSelectChange: (event: React.ChangeEvent<HTMLSelectElement>, key: "template_name", index?: number) => void;
  previousCompensationDetails?: PayrollTemplate;
  hiddenElements?: {
    // Add more hidden elements here
    effective_date?: boolean;
  };
  inputProps?: {
    effective_date?: CustomDateFieldProps;
  };
}) => {
  const preventScroll = (event: any) => {
    if (event.target.type === "number") {
      event.preventDefault();
    }
  };

  useEffect(() => {
    window.addEventListener("wheel", preventScroll, { passive: false });
    return () => window.removeEventListener("wheel", preventScroll);
  }, []);

  useEffect(() => {
    const init = () => {
      const isFormDataPresent = previousCompensationDetails && previousCompensationDetails?.components?.length > 0;
      const isTemplateChanged = formState?.template_name !== previousCompensationDetails?.template_name;

      if (isFormDataPresent && !isTemplateChanged) {
        const componentsWithIds = injectIds(previousCompensationDetails?.components || []);
        setComponentsData(componentsWithIds);
        return;
      }

      const selectedTemplate = data?.find((template) => template.name === formState.template_name);

      if (selectedTemplate) {
        const componentsWithIds = injectIds(selectedTemplate?.components || []);
        setComponentsData(componentsWithIds);
      }
    };
    init();
  }, [formState.template_name, data, previousCompensationDetails]);

  const handleAmountChange = (id: string, value: number) => {
    const updateAmount = (components: PayrollComponent[]): PayrollComponent[] => {
      return components.map((component) => {
        if (component.id === id) {
          return { ...component, amount: value };
        }
        if (component.sub_components.length > 0) {
          const updatedSubComponents = updateAmount(component.sub_components);
          const subTotal = updatedSubComponents.reduce((sum, subComp) => sum + (subComp.amount || 0), 0);
          return { ...component, sub_components: updatedSubComponents, amount: subTotal };
        }
        return component;
      });
    };
    setComponentsData((prev) => updateAmount(prev));
  };

  const totalCTC = useMemo(() => {
    return componentsData.reduce((total, component) => total + (component.amount || 0), 0);
  }, [componentsData]);

  if (
    isViewOnlyMode ||
    isCompensationAlreadyPresentForEmployee ||
    (previousCompensationDetails && previousCompensationDetails?.components?.length === 0)
  ) {
    return (
      <EmployeeCompensation
        compensation={{
          components: previousCompensationDetails?.components || [],
        }}
      />
    );
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Box display="flex" alignItems="center" gap={2}>
        <Box>
          <CustomSelect
            name="template"
            onChange={(e) => handleSelectChange(e as any, "template_name")}
            disabled={isViewOnlyMode || isCompensationAlreadyPresentForEmployee}
            required
            displayEmpty
            noDataMessage="No templates available"
            options={
              data?.map((template) => ({
                label: template.name,
                value: template.name,
              })) || []
            }
            label="Template"
            placeholder="Select a salary template"
            sx={{ minWidth: 300 }}
            size="small"
            value={formState?.template_name || ""}
          />
        </Box>
        {!hiddenElements?.effective_date && (
          <Box>
            <CustomDateField
              disabled={isViewOnlyMode || isCompensationAlreadyPresentForEmployee}
              name="effective_date"
              title="Effective Date"
              value={(formState?.effective_date as unknown as Date) || ""}
              slotProps={{
                textField: {
                  size: "small",
                },
              }}
              onChange={(e: Date | null) => setFormDetail("effective_date", e)}
              {...inputProps?.effective_date}
            />
          </Box>
        )}
      </Box>
      {componentsData.length > 0 && (
        <Box>
          <ContentHeader title="Salary Breakdown" subtitle="Breakdown of your current compensation" />
          <CompensationHierarchy
            components={componentsData}
            handleAmountChange={handleAmountChange}
            isCompensationAlreadyPresentForEmployee={isCompensationAlreadyPresentForEmployee}
            isViewOnlyMode={isViewOnlyMode}
          />
        </Box>
      )}
      <Divider sx={{ marginY: 4 }} />
      <TotalCTCContainer>
        <Typography variant="h6">Total CTC:</Typography>
        <Typography
          variant="h6"
          sx={{
            fontWeight: "bold",
            color: "primary.main",
          }}
        >
          {totalCTC} {componentsData[0]?.currency || ""}
        </Typography>
      </TotalCTCContainer>
    </Box>
  );
};

export default CompensationItem;
