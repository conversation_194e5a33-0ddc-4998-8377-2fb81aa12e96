import { Checkbox, FormControlLabel, Grid, Paper, styled } from "@mui/material";
import { addDays } from "date-fns";
import React from "react";
import useWorkRoleConfig from "src/customHooks/useWorkRoleConfig";
import CustomDateField from "src/modules/Common/FormInputs/CustomDateField";
import CustomSelect from "src/modules/Common/FormInputs/CustomSelect";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import { Feedback, PerformanceReviewRequest } from "src/services/api_definitions/performanceManagement.service";
import DetailListItem from "../DetailListItem";

export type PromotionFormDetail = any & {
  status: string;
  reason: string | null;
  effectiveDate: string;
};

type PromotionRecommendatoionProps = {
  reviewerType: Feedback["reviewer_type"];
  selectedRequest: PerformanceReviewRequest;
  promotion?: {
    promotionFormDetails: PromotionFormDetail;
    promotionFormErrors: Record<keyof PromotionFormDetail, string | undefined>;
    setPromotionFormDetail: (name: string, value: unknown, index?: number) => void;
  };
};

enum PromotionStatus {
  Approved = "Approved",
  Rejected = "Rejected",
}

const promotionStatusOptions = Object.values(PromotionStatus).map((status) => ({ label: status, value: status }));

const typography = {
  band: "Band",
  level: "Level",
  grade: "Grade",
  name: "Job Title", // job title name
  justification: "Justification",
  status: "Status",
  comments: "Comments",
  effectiveDate: "Effective Date",
  promotionRecommendation: "Recommendation",
  recommendForPromotion: "Recommend for Promotion",
};

const PromotionContainer = styled(Paper)({
  display: "flex",
  flexDirection: "column",
  gap: 2,
  padding: 3,
  elevation: 3, // elevation does not work here, must be passed as a prop
});

const PromotionRecommendation: React.FC<PromotionRecommendatoionProps> = ({
  reviewerType,
  selectedRequest,
  promotion,
}) => {
  const { getConfig, bandOptions, gradeOptions, levelOptions, filterGrades, filterLevels, jobTitles, filterJobTitles } =
    useWorkRoleConfig({
      businessUnit: selectedRequest?.business_unit,
      department: selectedRequest?.department,
    });

  const inputConfig = {
    band: (
      <CustomSelect
        options={bandOptions?.map((val) => ({ label: val.name, value: val.name })) || []}
        name="band"
        title="Band"
        label="Band"
        size="small"
        required
        fullWidth
        value={promotion?.promotionFormDetails?.band || ""}
        onChange={(e) => {
          promotion?.setPromotionFormDetail("band", e.target.value);
          promotion?.setPromotionFormDetail("level", "");
          promotion?.setPromotionFormDetail("grade", "");
          promotion?.setPromotionFormDetail("jobTitle", "");
        }}
      />
    ),
    level: (
      <CustomSelect
        options={
          filterLevels(promotion?.promotionFormDetails?.band || "", levelOptions || [])?.map((level) => ({
            label: level.name,
            value: level?.name,
          })) || []
        }
        name="level"
        title="Level"
        label="Level"
        required
        size="small"
        fullWidth
        value={promotion?.promotionFormDetails?.level || ""}
        onChange={(e) => {
          promotion?.setPromotionFormDetail("level", e.target.value);
          promotion?.setPromotionFormDetail("grade", "");
          promotion?.setPromotionFormDetail("jobTitle", "");
        }}
      />
    ),
    grade: (
      <CustomSelect
        options={filterGrades(
          promotion?.promotionFormDetails?.band || "",
          promotion?.promotionFormDetails?.level || "",
          gradeOptions || [],
        )?.map((grade) => ({ label: grade.name, value: grade.name }))}
        name="grade"
        title="Grade"
        label="Grade"
        required
        size="small"
        fullWidth
        value={promotion?.promotionFormDetails?.grade || ""}
        onChange={(e) => {
          promotion?.setPromotionFormDetail("grade", e.target.value);
          promotion?.setPromotionFormDetail("jobTitle", "");
        }}
      />
    ),
    jobTitle: (
      <CustomSelect
        options={filterJobTitles(
          promotion?.promotionFormDetails?.band || "",
          promotion?.promotionFormDetails?.level || "",
          promotion?.promotionFormDetails?.grade || "",
          jobTitles || [],
        )?.map((jobTitle) => ({ label: jobTitle.name, value: jobTitle.name }))}
        name="jobTitle"
        required
        title={typography.name}
        label={typography.name}
        size="small"
        fullWidth
        value={promotion?.promotionFormDetails?.jobTitle || ""}
        onChange={(e) => {
          promotion?.setPromotionFormDetail("jobTitle", e.target.value);
        }}
      />
    ),
  };

  const getWorkDetailInputs = () => {
    const { hierarchy } = getConfig();
    const columns = hierarchy?.length;
    console.log({ hierarchy });
    return hierarchy.map((key) => {
      return (
        <Grid key={key} item sm={12 / columns}>
          {inputConfig?.[key]}
        </Grid>
      );
    });
  };

  const getViewsWrtReviewerType = () => {
    switch (reviewerType) {
      case "manager":
        return (
          <PromotionContainer sx={{ padding: 3 }}>
            {reviewerType === "manager" && (
              <Grid container spacing={2}>
                <Grid item sm={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={promotion?.promotionFormDetails?.isUpForPromotion}
                        onChange={(_ev, checked) =>
                          promotion?.setPromotionFormDetail("isUpForPromotion", checked as unknown as string)
                        }
                      />
                    }
                    label={typography.recommendForPromotion}
                  />
                </Grid>
                {promotion?.promotionFormDetails?.isUpForPromotion && getWorkDetailInputs()}
                {promotion?.promotionFormDetails?.isUpForPromotion && (
                  <Grid item sm={12}>
                    <CustomTextField
                      name="justification"
                      required
                      title={typography.justification}
                      value={promotion?.promotionFormDetails?.justification || ""}
                      fullWidth
                      multiline
                      rows={3}
                      onChange={(e) => promotion?.setPromotionFormDetail("justification", e.target.value)}
                    />
                  </Grid>
                )}
              </Grid>
            )}
          </PromotionContainer>
        );
      case "hrbp": {
        if (!selectedRequest?.review?.promotion) {
          return null;
        }
        const jobTitlesToDisplay = selectedRequest?.review?.promotion?.job_title
          ? [
              {
                title: typography.band,
                value: selectedRequest?.review?.promotion?.job_title?.band,
              },
              {
                title: typography.level,
                value: selectedRequest?.review?.promotion?.job_title?.level,
              },
              {
                title: typography.grade,
                value: selectedRequest?.review?.promotion?.job_title?.grade,
              },
              {
                title: typography.name,
                value: selectedRequest?.review?.promotion?.job_title?.name,
              },
            ].filter((item) => item.value)
          : [];

        const columnDivider = 12 / jobTitlesToDisplay.length;
        return (
          <PromotionContainer sx={{ padding: 3 }}>
            <Grid container spacing={2}>
              {jobTitlesToDisplay.map(({ title, value }) => (
                <Grid key={title} item sm={columnDivider}>
                  <DetailListItem title={title} value={value} />
                </Grid>
              ))}
              <Grid item sm={12}>
                <DetailListItem
                  title={typography.justification}
                  value={selectedRequest?.review?.promotion?.justification}
                />
              </Grid>
              <Grid item sm={4}>
                <CustomSelect
                  name="status"
                  label={typography.promotionRecommendation}
                  size="small"
                  required
                  fullWidth
                  value={promotion?.promotionFormDetails?.status || ""}
                  onChange={(e) => promotion?.setPromotionFormDetail("status", e.target.value)}
                  options={promotionStatusOptions}
                />
              </Grid>
              {promotion?.promotionFormDetails?.status === "Approved" && (
                <Grid item sm={4}>
                  <CustomDateField
                    name="effectiveDate"
                    title={typography.effectiveDate}
                    fullWidth
                    minDate={addDays(new Date(), 1)}
                    required={promotion?.promotionFormDetails?.status === PromotionStatus.Approved}
                    slotProps={{
                      textField: {
                        size: "small",
                        fullWidth: true,
                      },
                    }}
                    value={promotion?.promotionFormDetails?.effectiveDate || ""}
                    onChange={(date: Date) => promotion?.setPromotionFormDetail("effectiveDate", date)}
                  />
                </Grid>
              )}
              <Grid item sm={12}>
                <CustomTextField
                  name="reason"
                  title={typography.comments}
                  required
                  fullWidth
                  value={promotion?.promotionFormDetails?.reason || ""}
                  onChange={(e) => promotion?.setPromotionFormDetail("reason", e.target.value)}
                  multiline
                  rows={3}
                />
              </Grid>
            </Grid>
          </PromotionContainer>
        );
      }

      default:
        return null;
    }
  };

  return getViewsWrtReviewerType();
};

export default PromotionRecommendation;
