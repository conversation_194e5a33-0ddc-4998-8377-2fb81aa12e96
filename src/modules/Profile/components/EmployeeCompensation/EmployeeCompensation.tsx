import { Box, Container, Typography, useTheme } from "@mui/material";
import { MRT_ColumnDef } from "material-react-table";
import React, { useMemo } from "react";
import DataTable from "src/modules/Common/Table/DataTable";
import { PayrollComponent, PayrollTemplate } from "src/services/api_definitions/payroll.service";

type Props = {
  compensation: Pick<PayrollTemplate, "components">;
  locale?: string;
};

//Todo: Remove this any type and add proper types
export const formatCurrency = (amount: number, currency: string, locale: string = "IN") => {
  // this null undefined check is to show 0's if present in comp
  if (amount === null || amount === undefined || !locale) {
    return "";
  }
  return new Intl.NumberFormat(`en-${locale}`, { style: "currency", currency }).format(amount);
};

export const calculateTotalCTC = (data: PayrollComponent[] = []): number =>
  data.reduce((total, item) => total + (item?.amount || 0), 0);

const EmployeeCompensation: React.FC<Props> = ({ compensation, locale = "IN" }) => {
  const theme = useTheme();
  const compensationCurrency = compensation?.components?.[0]?.currency || "INR";
  const totalCTC = useMemo(() => calculateTotalCTC(compensation?.components || []), [compensation]);

  const columns = useMemo<MRT_ColumnDef<any>[]>(
    () => [
      {
        accessorKey: "name",
        header: "Component",
        size: 300,
        Cell: ({ row }) => (
          <Box sx={{ paddingLeft: `${row.depth * 20}px`, fontWeight: row.depth === 0 ? 600 : 400 }}>
            {row.original.name}
          </Box>
        ),
      },
      {
        accessorKey: "amount",
        header: "Amount",
        size: 150,
        accessorFn: (row) => formatCurrency(row.amount, row.currency, locale),
      },
    ],
    [],
  );

  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          mb: 1,
          borderRadius: 4,
          p: 2,
          bgcolor: theme.palette.primary.main,
          color: theme.palette.primary.contrastText,
        }}
      >
        <Typography variant="h4">{`CTC: ${formatCurrency(totalCTC, compensationCurrency, locale)}`}</Typography>
      </Box>
      <DataTable
        columns={columns}
        data={compensation.components || []}
        state={{
          expanded: true,
        }}
        enableStickyHeader
        enableExpanding
        muiTableContainerProps={{
          sx: {
            maxHeight: "100%",
            height: "100%",
          },
        }}
        getSubRows={(row: PayrollComponent) => row?.sub_components || []}
      />
    </Container>
  );
};

export default EmployeeCompensation;
