const PATH_CONFIG = {
  HOME: {
    key: "dashboard",
    path: "/dashboard",
    isRoot: true,
  },
  /* Dashboard Routes */
  DASHBOARD_TIME_SHEETS: {
    key: "dashboard_timeSheets",
    path: "/dashboard/time-sheets",
  },
  DASHBOARD_HOLIDAYS: {
    key: "dashboard_holidays",
    path: "/dashboard/holidays",
  },
  DASHBOARD_CALENDAR: {
    key: "dashboard_calendar",
    path: "/dashboard/calendar",
  },
  DASHBOARD_CELEBRATION: {
    key: "dashboard_celebration",
    path: "/dashboard/celebration",
  },
  DASHBOARD_TODAYS_TEAM_STATUS: {
    key: "dashboard_todaysTeamStatus",
    path: "/dashboard/todays-team-status",
  },
  DASHBOARD_LEAVE_REPORTS: {
    key: "dashboard_leaveReports",
    path: "/dashboard/leave-reports",
  },
  DASHBOARD_LEAVES: {
    key: "dashboard_leaves",
    path: "/dashboard/leaves",
  },
  DASHBOARD_ATTENDANCE: {
    key: "dashboard_attendance",
    path: "/dashboard/attendance",
  },
  DASHBOARD_NEW_JOINEES: {
    key: "dashboard_newJoiners",
    path: "/dashboard/new-joiners",
  },
  DASHBOARD_EMPLOYEE_SEPERATIONS: {
    key: "dashboard_seperations",
    path: "/dashboard/seperations",
  },
  /* ******************** */
  REPORTS: {
    key: "reports",
    path: "/reports",
    isRoot: true,
  },
  LEADS: {
    key: "leads",
    path: "/leads",
    isRoot: true,
  },
  CALENDAR: {
    key: "calendar",
    path: "/calendar",
    isRoot: true,
  },
  LEAVES: {
    key: "leaves",
    path: "/leaves",
    isRoot: true,
  },
  TENANTS: {
    key: "tenants",
    path: "/tenants",
    isRoot: true,
  },
  TENANTS_LIST: {
    key: "tenants_allTenants",
    path: "/tenants/list",
  },
  TENANTS_EDIT: {
    key: "tenants_tenantConfiguration",
    path: "/tenants/:tenantId/:tenantName/edit",
  },
  EMPLOYEES: {
    key: "employees",
    path: "/employees",
    isRoot: true,
  },
  EMPLOYEE_PROFILE: {
    key: "employee_profile",
    path: "/employee/:employeeId",
    isRoot: true,
    isInternal: true,
  },
  NEWJOINEES: {
    key: "newHires",
    path: "/newjoinees",
  },
  TASKS: {
    key: "tasks",
    path: "/tasks",
    isRoot: true,
  },
  PROFILE: {
    key: "profile",
    path: "/profile",
    isRoot: true,
  },
  ONBOARDING: {
    key: "onboarding",
    path: "/onboarding",
    isRoot: true,
  },
  USERS: {
    key: "users",
    path: "/users",
    isRoot: true,
  },
  SETTINGS: {
    key: "settings",
    path: "/settings",
    isRoot: true,
  },
  LOGIN: {
    key: "login",
    path: "/login",
    isRoot: true,
  },
  SCREEN_MANAGEMENT: {
    key: "screenUri",
    path: "/screen-management",
    isRoot: true,
  },
  EMPLOYEE_ATTENDANCE: {
    key: "attendance",
    path: "/attendance",
    isRoot: true,
  },
  EMPLOYEE_DOCUMENTS: {
    key: "documents",
    path: "/documents",
  },
  SCREEN_MANAGEMENT_MANAGE_SCREEN_URIS: {
    key: "screenUri_manageScreenURIs",
    path: "/screen-management",
  },
  SCREEN_MANAGEMENT_MANAGE_SCREENS: {
    key: "screenUri_manageScreens",
    path: "/screen-management",
  },
  BUSINESS_UNIT: {
    key: "settings_businessUnits",
    path: "/settings/business-units",
  },
  DEPARTMENT: {
    key: "settings_departments",
    path: "/settings/departments",
  },
  SUB_DEPARTMENT: {
    key: "settings_subDepartments",
    path: "/settings/sub-departments",
  },
  TEAM: {
    key: "settings_teams",
    path: "/settings/teams",
  },
  JOB_FAMILY: {
    key: "settings_jobFamilies",
    path: "/settings/job-families",
  },
  JOB_TITLE: {
    key: "settings_jobTitles",
    path: "/settings/job-titles",
  },
  CONFIGURATION: {
    key: "settings_configurations",
    path: "/settings/configurations",
  },
  EMPLOYEE_ID_CONFIGURATION: {
    key: "settings_configurations_employeeIdFormat",
    path: "/settings/configurations/employee-id-configuration",
  },
  ATTENDANCE_CONFIGURATION: {
    key: "settings_configurations_attendance",
    path: "/settings/configurations/attendance-configuration",
  },
  ROLE_MANAGEMENT: {
    key: "settings_roleManagement",
    path: "/settings/role-management",
  },
  ALL_ROLES: {
    key: "settings_roleManagement_allRoles",
    path: "/settings/role-management/all-roles",
  },
  MANAGE_ROLES: {
    key: "settings_roleManagement_manageRoles",
    path: "/settings/role-management/manage-roles",
  },
  MANAGE_HIERARCHIAL_ROLES: {
    key: "settings_roleManagement_manageRoleHierarchies",
    path: "/settings/role-management/manage-role-hierarchies",
  },
  COST_CENTER: {
    key: "settings_costCenters",
    path: "/settings/role-management/cost-centers",
  },
  WORK_ROLE: {
    key: "settings_workRoles",
    path: "/settings/role-management/work-roles",
  },
  LEAVE_MANAGEMENT: {
    key: "settings_leaveManagement",
    path: "/settings/leave-management",
  },
  LEAVE_MANAGEMENT_HOLIDAYS: {
    key: "settings_leaveManagement_holidays",
    path: "/settings/leave-management/holidays",
  },
  LEAVE_MANAGEMENT_POLICIES: {
    key: "settings_leaveManagement_leavePolicies",
    path: "/settings/leave-management/leave-policies",
  },
  LEAVE_MANAGEMENT_LEAVE_TYPES: {
    key: "settings_leaveManagement_leaveTypes",
    path: "/settings/leave-management/leave-types",
  },
  ORGANISATIONS: {
    key: "settings_organisations",
    path: "/settings/organisations",
  },
  TIME_SHEETS: {
    key: "timesheets",
    path: "/time-sheets",
    isRoot: true,
  },
  TEAM_CALENDAR: {
    key: "team_calendar",
    path: "/team-calendar",
  },
  SETTINGS_PERFORMANCEMANAGEMENT: {
    key: "settings_performanceManagement",
    path: "/settings/performance-management",
  },
  SETTINGS_PERFORMANCEMANAGEMENT_RATINGSCONFIG: {
    key: "settings_performanceManagement_ratings",
    path: "settings/performance-management/ratings-config",
  },
  SETTINGS_PERFORMANCEMANAGEMENT_REVIEWCYCLES: {
    key: "settings_performanceManagement_reviewCycles",
    path: "settings/performance-management/review-cycles",
  },
  PEFRORMANCE_MANAGEMENT: {
    key: "performanceManagement",
    path: "/performance-management",
    isRoot: true,
  },
  PAGE_NOT_FOUND: {
    key: "page-not-found",
    path: "/*",
  },
  EMPLOYEE_OFFBOARDING: {
    key: "offboarding",
    path: "/offboarding",
    isRoot: true,
  },
  SETTINGS_OFFBOARDING: {
    key: "settings_offboarding",
    path: "/settings/offboarding",
  },
  SETTINGS_PROBATIONS: {
    key: "settings_onboarding",
    path: "/settings/onboarding",
  },
  EMPLOYEE_SEPERATIONS: {
    key: "separations",
    path: "/separations",
    isRoot: true,
  },
  EMPLOYEE_SEPERATION: {
    key: "separation",
    path: "/separation",
    isRoot: true,
  },
  PAYROLL: {
    key: "payroll",
    path: "/payroll",
    isRoot: true,
  },
  INSURANCE: {
    key: "insurance",
    path: "/insurance",
    isRoot: true,
  },
  SETTINGS_PAYROLL: {
    key: "settings_compensation",
    path: "/settings/compensation",
  },
  PROJECT_TRACKING: {
    key: "projectTracking",
    path: "/project-tracking",
    isRoot: true,
  },
  TIMESHEET_MANAGEMENT: {
    key: "clientProjects",
    path: "/client-projects",
    isRoot: true,
  },
  TIMESHEET_TRACKING: {
    key: "timeLog",
    path: "/time-log",
  },
};

export type PathConfig = typeof PATH_CONFIG & {
  isRoot?: boolean;
};
export { PATH_CONFIG };
