import { CloseRounded } from "@mui/icons-material";
import { <PERSON><PERSON><PERSON>, Grid2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Typography } from "@mui/material";
import { Box } from "@mui/system";
import { useStore } from "@tanstack/react-form";
import React, { useEffect } from "react";
import { PayrollComponentV2, PayrollTemplateV2 } from "../../../../services/api_definitions/payroll.service";

type Props = {
  form: any;
  selectedRow: PayrollTemplateV2;
  selectedComponentTypes: Record<string, PayrollComponentV2[]>;
  setSelectedComponentTypes: React.Dispatch<React.SetStateAction<Record<string, PayrollComponentV2[]>>>;
  allComponents?: PayrollComponentV2[];
  isEdit: boolean;
};

const DynamicPayrollInputs: React.FC<Props> = ({
  form,
  selectedComponentTypes,
  setSelectedComponentTypes,
  isEdit,
  selectedRow,
}) => {
  const getComponentNameWRTComponentFormulaType = (_component: PayrollComponentV2, index: number) => {
    return selectedComponentTypes[_component.component_type]?.[index]?.name;
  };

  useEffect(() => {
    let globalIndex = 0;

    Object.keys(selectedComponentTypes)?.forEach((_eachComponentType) => {
      selectedComponentTypes[_eachComponentType]?.forEach((_eachComponent: PayrollComponentV2) => {
        form.setFieldValue(`components[${globalIndex}].component_name`, _eachComponent?.name);
        const componentValueKey = `components[${globalIndex}].component_value`;
        const componentTypeKey = `components[${globalIndex}].component_type`;

        if (!isEdit) {
          form.setFieldValue(componentValueKey, _eachComponent?.formula.value);
          form.setFieldValue(componentTypeKey, _eachComponent?.component_type);
        }

        const requiredSelectedRow = selectedRow?.components?.find(
          (selectedRowComponent) => selectedRowComponent.name === _eachComponent.name,
        );

        form.setFieldValue(componentValueKey, requiredSelectedRow?.formula?.value || _eachComponent?.formula.value);
        form.setFieldValue(componentTypeKey, requiredSelectedRow?.component_type || _eachComponent?.component_type);

        globalIndex++;
      });
    });
  }, [selectedComponentTypes]);

  const getValueFieldWRTComponentFormulaType = (component: PayrollComponentV2, idx: number) => {
    switch (component.formula.calculation_type) {
      case "Flat":
        return (
          <form.AppField name={`components[${idx}].component_value`}>
            {(field: any) => <field.EffiCurrency label="" currency={component?.currency} endHelperText="Annual" />}
          </form.AppField>
        );
      case "Percentage":
        return (
          <form.AppField name={`components[${idx}].component_value`}>
            {(field: any) => (
              <field.EffiPercentageField label="" endHelperText={`of ${component?.formula?.display_name}`} />
            )}
          </form.AppField>
        );
      case "Formula":
        return (
          <form.AppField name={`components[${idx}].component_value`}>
            {(field: any) => <field.EffiCurrency label="" />}
          </form.AppField>
        );
      case "SystemDefined":
        return (
          <Grid2 container justifyContent="center">
            <Grid2 size={12}>
              <Typography variant="body1" sx={{ fontStyle: "italic", color: "text.secondary" }}>
                System Defined
              </Typography>
            </Grid2>
          </Grid2>
        );
      default:
        return null;
    }
  };

  const deleteRow = (field: any, component: PayrollComponentV2, index: number) => {
    field.removeValue(index);
    const newSelectedComponentTypes = { ...selectedComponentTypes };
    const updatedSelectedComponentTypes = newSelectedComponentTypes[component.component_type]?.filter(
      (eachComponent: PayrollComponentV2) => eachComponent.name !== component.name,
    );
    newSelectedComponentTypes[component.component_type] = updatedSelectedComponentTypes;
    if (updatedSelectedComponentTypes?.length === 0) {
      delete newSelectedComponentTypes[component.component_type];
    }
    setSelectedComponentTypes(newSelectedComponentTypes);
  };

  return (() => {
    let globalIndex = 0;

    return Object.keys(selectedComponentTypes)?.map((_eachComponentType, typeIndex) => (
      <Box key={typeIndex} display="flex" flexDirection="column" gap={2}>
        <Typography variant="h6" fontWeight={600} fontSize={18}>
          {`${_eachComponentType}s`}
        </Typography>
        <Divider orientation="horizontal" />
        {selectedComponentTypes[_eachComponentType]?.map((_eachComponent: PayrollComponentV2, idx: number) => {
          const currentGlobalIndex = globalIndex++;
          return (
            <>
              <form.Field name="components" key={`${typeIndex}-${idx}`} mode="array">
                {(_subField: any) => (
                  <Grid2 container spacing={1} justifyContent="center" alignItems="center">
                    <Grid2 size={5} justifySelf="center">
                      <Typography
                        variant="body2"
                        sx={{
                          whiteSpace: "nowrap",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          width: 250,
                        }}
                      >
                        {getComponentNameWRTComponentFormulaType(_eachComponent, idx) || "Component Name"}
                      </Typography>
                    </Grid2>
                    <Grid2 size={6}>{getValueFieldWRTComponentFormulaType(_eachComponent, currentGlobalIndex)}</Grid2>
                    <Grid2 size={1} textAlign="center">
                      <IconButton
                        onClick={() => deleteRow(_subField, _eachComponent, currentGlobalIndex)}
                        sx={{ visibility: _eachComponent?.mandatory ? "hidden" : "visible" }}
                      >
                        <CloseRounded />
                      </IconButton>
                    </Grid2>
                  </Grid2>
                )}
              </form.Field>
              <Divider key={`${typeIndex}-${idx}-divider`} />
            </>
          );
        })}
      </Box>
    ));
  })();
};

export default DynamicPayrollInputs;
