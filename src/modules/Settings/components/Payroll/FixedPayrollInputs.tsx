import { useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";
import departmentService from "src/services/department.service";
import { useMasterData } from "../../../../customHooks/useMasterData";
import EffiDynamicForm, { InputFormStruct } from "../../../Common/Form/components/EffiDynamicForm";

type FixedPayrollInputsProps = {
  form: any;
  isEdit: boolean;
  selectedRow?: PayrollTemplateV2;
};

const FixedPayrollInputs: React.FC<FixedPayrollInputsProps> = ({ form, selectedRow, isEdit }) => {
  const { selectedOrganisationDetails } = useAppSelector((state) => state.userManagement);
  const countryProps = useMemo(() => {
    const countrySet = selectedOrganisationDetails?.addresses?.reduce((acc, curr) => {
      acc.add(curr?.country as string);
      return acc;
    }, new Set());
    const countriesToDisplay = [...countrySet].map((country) => ({
      label: country,
      value: country,
    }));

    const isVisible = countriesToDisplay.length > 1;

    if (!isVisible) {
      form.setFieldValue("country", countriesToDisplay[0].value);
    }
    return {
      countryOptions: countriesToDisplay,
      isVisible: countriesToDisplay.length > 1,
    };
  }, [selectedOrganisationDetails]);

  const { data: jobTitles } = useQuery(["get-all-job-titles"], async () => await departmentService.getAllJobTitles(), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
  const { data: employeeTypes } = useMasterData("EmployeeType", {
    refetchOnMount: true,
  });
  const employeeTypesOptions = useMemo(() => {
    return employeeTypes?.map((employeeType) => ({
      label: employeeType,
      value: employeeType,
    }));
  }, [employeeTypes]);

  const jobTitleOptions = useMemo(
    () =>
      jobTitles?.map((jobTitle) => ({
        label: jobTitle?.formatted_name as string,
        value: jobTitle?.formatted_name as string,
        disabled: selectedRow?.job_titles?.includes(jobTitle?.formatted_name as string),
      })),
    [jobTitles, selectedRow?.job_titles],
  );

  const inputFields: InputFormStruct[] = useMemo(
    () => [
      {
        fieldProps: {
          name: "name",
        },
        formProps: {
          label: "Template Name",
          type: "text",
          required: true,
          disabled: isEdit,
        },
        containerProps: {
          size: 12,
        },
      },
      {
        fieldProps: {
          name: "description",
        },
        formProps: {
          label: "Description",
          type: "text",
        },
        containerProps: {
          size: 12,
        },
      },
      {
        fieldProps: {
          name: "employee_types",
        },
        formProps: {
          label: "Applicable Employee Type(s)",
          type: "multi-select",
          options: employeeTypesOptions,
          required: true,
          placeholder: "Select Employee Types",
        },
        containerProps: {
          size: 5,
        },
      },
      {
        fieldProps: {
          name: "job_titles",
        },
        formProps: {
          label: "Applicable Job Title(s)",
          type: "multi-select",
          options: jobTitleOptions,
          required: true,
          placeholder: "Select Job Titles",
        },
        containerProps: {
          size: 6,
        },
      },
    ],
    [jobTitleOptions, countryProps, employeeTypesOptions],
  );

  return <EffiDynamicForm form={form} inputFields={inputFields} />;
};

export default FixedPayrollInputs;
