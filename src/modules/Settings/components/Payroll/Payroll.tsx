import { Container } from "@mui/material";
import React, { useMemo, useState } from "react";
import { PayrollTemplate, PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";
import AddEditTemplate from "./AddEditTemplate";
import AllTemplates from "./AllTemplates";
import ViewTemplate from "./ViewTemplate";

export enum PayrollViewModes {
  VIEW_ALL = "view-all",
  VIEW = "view_payroll",
  ADD = "add_payroll",
  EDIT = "edit_payroll",
}

export type PayrollTemplateProps = {
  setCurrentSelectedMode: (selectedMode: PayrollViewModes) => void;
  currentSelectedMode: PayrollViewModes;
  setSelectedRow?: (row: PayrollTemplateV2 | null) => void;
  selectedRow?: PayrollTemplateV2 | null;
};

const Payroll = () => {
  const [currentSelectedMode, setCurrentSelectedMode] = useState<PayrollViewModes>(PayrollViewModes.VIEW_ALL);
  const [selectedRow, setSelectedRow] = React.useState<PayrollTemplate | null>(null);
  const isEdit = useMemo(
    () => currentSelectedMode === PayrollViewModes.EDIT && selectedRow !== null,
    [selectedRow, currentSelectedMode],
  );

  const payrollViews = () => {
    switch (currentSelectedMode) {
      case PayrollViewModes.VIEW_ALL:
        return (
          <AllTemplates
            setCurrentSelectedMode={setCurrentSelectedMode}
            currentSelectedMode={currentSelectedMode}
            setSelectedRow={setSelectedRow}
          />
        );
      case PayrollViewModes.VIEW:
        return (
          <ViewTemplate
            setCurrentSelectedMode={setCurrentSelectedMode}
            currentSelectedMode={currentSelectedMode}
            selectedRow={selectedRow}
          />
        );
      case PayrollViewModes.ADD:
        return (
          <AddEditTemplate
            setCurrentSelectedMode={setCurrentSelectedMode}
            currentSelectedMode={currentSelectedMode}
            selectedRow={null}
            setSelectedRow={setSelectedRow}
            isEdit={false}
          />
        );
      case PayrollViewModes.EDIT:
        return (
          <AddEditTemplate
            setCurrentSelectedMode={setCurrentSelectedMode}
            currentSelectedMode={currentSelectedMode}
            selectedRow={selectedRow}
            setSelectedRow={setSelectedRow}
            isEdit={isEdit}
          />
        );
      default:
        throw new Error(`Unsupported mode: ${currentSelectedMode}`);
    }
  };

  return (
    <Container maxWidth={false} disableGutters sx={{ height: "100%", padding: 0, margin: 0, overflow: "auto" }}>
      {payrollViews()}
    </Container>
  );
};

export default Payroll;
