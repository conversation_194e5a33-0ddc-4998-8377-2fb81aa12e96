import React from "react";
import DataTable from "src/modules/Common/Table/DataTable";
import { PayrollComponent } from "src/services/api_definitions/payroll.service";
import { PayrollTemplateProps } from "./Payroll";

const ReadonlyPayrollHierarchy: React.FC<Pick<PayrollTemplateProps, "selectedRow">> = ({ selectedRow }) => {
  const getSubRows = (row: PayrollComponent) => row?.sub_components || [];

  return (
    <DataTable
      layoutMode="grid-no-grow"
      columns={[
        {
          accessorKey: "name",
          header: "Component Name",
          size: 300,
        },
        {
          accessorKey: "currency",
          header: "Currency",
          size: 250,
        },
      ]}
      data={selectedRow?.components || []}
      getSubRows={(originalRow) => getSubRows(originalRow)} // Access subrows
      initialState={{ expanded: true }} // Optional: Expand all rows initially
      muiTableBodyRowProps={() => ({
        sx: {
          borderBottom: "1px solid #ddd", // Optional: Add row separators
        },
      })}
      muiTableBodyCellProps={({ cell, row }) => ({
        sx: {
          ...(cell.column.id === "name" && {
            fontWeight: row.depth === 0 ? "bold" : "normal",
            paddingLeft: row.depth > 0 ? `${row.depth * 8}px` : "inherit",
          }),
        },
      })}
      muiTableHeadCellProps={({ column }) => ({
        sx: {
          ...(column.id === "name" && {
            textAlign: "left", // Ensure header aligns with the root column
            paddingLeft: "inherit", // Prevent indentation in the header
          }),
        },
      })}
      enableExpanding
      enableExpandAll
    />
  );
};

export default ReadonlyPayrollHierarchy;
