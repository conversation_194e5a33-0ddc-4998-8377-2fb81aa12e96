import React from "react";
import Modal from "src/modules/Common/Modal/Modal";
import AttendanceTrendModal from "src/modules/Dashboard/component/QuickViews/AttendanceTrendModal";
import { DataTableActionProps } from "./TeamCalendarView";

const AttendanceTrendsModal: React.FC<DataTableActionProps> = ({ onClose, selectedRow, teamCalendarDetails }) => {
  console.log({ onClose, selectedRow, teamCalendarDetails });

  return (
    <Modal
      isOpen
      onClose={onClose}
      title={`${selectedRow?.reportee?.display_name}'s Attendance Trends`}
      showBackButton
      showDivider
    >
      <AttendanceTrendModal attendanceDetails={selectedRow?.attendance} />
    </Modal>
  );
};

export default AttendanceTrendsModal;
