import { Box, Tab, Tabs } from "@mui/material";
import React, { useEffect, useMemo } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useAppSelector } from "src/customHooks/useAppSelector";
import useSubroutes from "src/customHooks/useSubroutes";
import NoAccessScreen from "src/modules/Common/NoAccess/NoAccess";
import { PATH_CONFIG } from "src/modules/Routing/config";
import Configuration from "src/modules/Settings/components/Configuration/Configuration";
import LeaveManagement from "src/modules/Settings/components/LeaveManagement/components/LeaveManagement";
import Offboarding from "src/modules/Settings/components/Offboarding/components/Offboarding";
import Probations from "src/modules/Settings/components/Onboarding/components/Probations";
import { BusinessUnit } from "src/modules/Settings/components/OrganisationSettings/components/BusinessUnits";
import { Departments } from "src/modules/Settings/components/OrganisationSettings/components/Departments";
import { JobFamily } from "src/modules/Settings/components/OrganisationSettings/components/JobFamily";
import { JobTitle } from "src/modules/Settings/components/OrganisationSettings/components/JobTitle";
import { SubDepartment } from "src/modules/Settings/components/OrganisationSettings/components/SubDepartment";
import { Team } from "src/modules/Settings/components/OrganisationSettings/components/Team";
import Organisations from "src/modules/Settings/components/Organisations/components/Organisations";
import Payroll from "src/modules/Settings/components/Payroll/Payroll";
import PerformanceManagement from "src/modules/Settings/components/PerformanceManagement/components/PerformanceManagement";
import RoleManagement from "src/modules/Settings/components/RoleManagement/RoleManagement";
import CostCentre from "src/modules/Tenants/components/CostCentre";
import TenantWorkRoles from "src/modules/Tenants/components/TenantWorkRoles";
import { addBreadcrumb, removeBreadcrumb } from "src/store/slices/breadcrumbs.slice";

const organisationTabs = [
  {
    key: PATH_CONFIG.ORGANISATIONS.key,
    path: PATH_CONFIG.ORGANISATIONS.path,
    label: "Organisations",
    component: <Organisations />,
    id: 0,
  },
  {
    key: PATH_CONFIG.BUSINESS_UNIT.key,
    path: PATH_CONFIG.BUSINESS_UNIT.path,
    label: "Business Units",
    component: <BusinessUnit />,
    id: 1,
  },
  {
    key: PATH_CONFIG.DEPARTMENT.key,
    path: PATH_CONFIG.DEPARTMENT.path,
    label: "Departments",
    component: <Departments />,
    id: 2,
  },
  {
    key: PATH_CONFIG.SUB_DEPARTMENT.key,
    path: PATH_CONFIG.SUB_DEPARTMENT.path,
    label: "Sub Departments",
    component: <SubDepartment />,
    id: 3,
  },

  {
    key: PATH_CONFIG.JOB_FAMILY.key,
    path: PATH_CONFIG.JOB_FAMILY.path,
    label: "Job Families",
    component: <JobFamily />,
    id: 4,
  },
  {
    key: PATH_CONFIG.JOB_TITLE.key,
    path: PATH_CONFIG.JOB_TITLE.path,
    label: "Job Titles",
    component: <JobTitle />,
    id: 5,
  },
  {
    key: PATH_CONFIG.TEAM.key,
    path: PATH_CONFIG.TEAM.path,
    label: "Teams",
    component: <Team />,
    id: 6,
  },
  {
    key: PATH_CONFIG.CONFIGURATION.key,
    path: PATH_CONFIG.CONFIGURATION.path,
    label: "Configuration",
    component: <Configuration />,
    id: 7,
  },

  {
    key: PATH_CONFIG.ROLE_MANAGEMENT.key,
    path: PATH_CONFIG.ROLE_MANAGEMENT.path,
    label: "Role Management",
    component: <RoleManagement />,
    id: 8,
  },
  {
    key: PATH_CONFIG.COST_CENTER.key,
    path: PATH_CONFIG.COST_CENTER.path,
    label: "Cost Center",
    component: <CostCentre featureKey={PATH_CONFIG.COST_CENTER.key} />,
    id: 9,
  },
  {
    key: PATH_CONFIG.WORK_ROLE.key,
    path: PATH_CONFIG.WORK_ROLE.path,
    label: "Work Roles",
    component: <TenantWorkRoles isAddWorkRoleFeatureActive={true} featureKey={PATH_CONFIG.WORK_ROLE.key} />,
    id: 10,
  },
  {
    key: PATH_CONFIG.LEAVE_MANAGEMENT.key,
    path: PATH_CONFIG.LEAVE_MANAGEMENT.path,
    label: "Leave Management",
    component: <LeaveManagement />,
    id: 11,
  },
  {
    key: PATH_CONFIG.SETTINGS_PERFORMANCEMANAGEMENT.key,
    path: PATH_CONFIG.SETTINGS_PERFORMANCEMANAGEMENT.path,
    label: "Performance Management",
    component: <PerformanceManagement />,
    id: 11,
  },
  {
    key: PATH_CONFIG.SETTINGS_OFFBOARDING.key,
    path: PATH_CONFIG.SETTINGS_OFFBOARDING.path,
    label: "Offboarding",
    component: <Offboarding />,
    id: 12,
  },
  {
    key: PATH_CONFIG.SETTINGS_PROBATIONS.key,
    path: PATH_CONFIG.SETTINGS_PROBATIONS.path,
    label: "Onboarding",
    component: <Probations />,
    id: 13,
  },
  {
    key: PATH_CONFIG.SETTINGS_PAYROLL.key,
    path: PATH_CONFIG.SETTINGS_PAYROLL.path,
    label: "Compensation",
    component: <Payroll />,
    id: 13,
  },
];

export default function OrganisationSettings() {
  const subRoutes = useSubroutes(PATH_CONFIG.SETTINGS.key);
  const { isFullView } = useAppSelector((state) => state.app);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const tabsToShow = useMemo(
    () => [
      ...organisationTabs
        .filter((settings) => subRoutes.some((route) => route.key === settings.key && route.acl?.canRead))
        .sort((a, b) => a.id - b.id),
    ],
    [subRoutes],
  );

  const [value, setValue] = React.useState(tabsToShow?.[0]?.key);

  // This is needed when we are changing roles or orgs & the url remains the same, a fallback check if we may presume
  useEffect(() => {
    const previousSelectedTab = tabsToShow?.find((eachTab) => eachTab.key === searchParams.get("tabId"));

    if (!previousSelectedTab) {
      dispatch(
        addBreadcrumb({
          isActive: true,
          isDisabled: false,
          label: tabsToShow?.[0]?.label,
          path: tabsToShow?.[0]?.key,
        }),
      );
      setValue(tabsToShow?.[0]?.key);
      navigate(PATH_CONFIG.SETTINGS.path);
      return;
    }
    dispatch(
      addBreadcrumb({
        isActive: true,
        isDisabled: false,
        label: previousSelectedTab.label,
        path: previousSelectedTab.key,
      }),
    );
    setValue(previousSelectedTab.key);
    navigate(`${PATH_CONFIG.SETTINGS.path}?tabId=${previousSelectedTab.key}`);
  }, []);

  const Component = useMemo(() => tabsToShow.find((eachTab) => eachTab.key === value)?.component, [value]);

  const handleChange = (_event: React.SyntheticEvent, newValue: string) => {
    const tab = tabsToShow.find((eachTab) => eachTab.key === newValue);
    if (tab) {
      dispatch(removeBreadcrumb(tabsToShow.find((eachTab) => eachTab.key === value)?.key || ""));
      dispatch(
        addBreadcrumb({
          isActive: true,
          isDisabled: false,
          label: tab?.label || "",
          path: tab?.key || "",
        }),
      );
      setValue(tab.key);
      navigate(`${PATH_CONFIG.SETTINGS.path}?tabId=${tab.key}`);
    }
  };

  if (!tabsToShow || tabsToShow?.length === 0) {
    return (
      <Box>
        <NoAccessScreen />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        height: "100%",
        display: "flex",
        justifyContent: "flex-start",
      }}
    >
      {!isFullView && (
        <Tabs
          orientation="vertical"
          variant="scrollable"
          value={value}
          onChange={handleChange}
          aria-label="Vertical tabs example"
          sx={{
            borderRight: 1,
            borderColor: "divider",
            minWidth: "fit-content",
            padding: 0,
          }}
        >
          {tabsToShow.map((tab) => (
            <Tab
              sx={{
                textTransform: "none",
                wordBreak: "break-word",
                width: "100%",
                alignItems: "flex-start",
                fontWeight: 500,
                color: "#667085",
              }}
              id={tab.key}
              value={tab.key}
              key={tab.key}
              label={tab.label}
            />
          ))}
        </Tabs>
      )}
      <Box sx={{ overflow: "auto", width: "100%", padding: isFullView ? 0 : "0px 16px" }}>{Component ?? null}</Box>
    </Box>
  );
}
