import Payroll from "../../modules/Settings/components/Payroll/Payroll";

export type PayrollTemplate = {
  name: string;
  country: string;
  job_titles: string[];
  effective_date: string;
  effective_from: string;
  effective_to: string;
  template_name: string;
  components: PayrollComponent[];
};

export type PayrollTemplateV2 = Omit<PayrollTemplate, "components"> & {
  description: string | null;
  active: boolean;
  employee_types: string;
  components: PayrollComponentV2[];
  active: boolean;
};

export type EmployeeAdminConfig = Pick<PayrollTemplate, "components" | "effective_date" | "template_name">;

type PayrollComponent = {
  id: string;
  name: string;
  currency: string;
  amount: number;
  sub_components: PayrollComponent[];
};

type PayrollComponentV2 = Pick<PayrollComponent, "id" | "name" | "currency"> & {
  id?: string;
  sort_order: number;
  mandatory: boolean;
  formula: Formulae;
  component_type: string;
};

enum CalculationTypes {
  Flat = "Flat",
  Percentage = "Percentage",
  Formula = "Formula",
  SystemDefined = "SystemDefined",
}

export type Formulae = {
  value: string;
  code: string | null;
  calculation_type: CalculationTypes;
  display_name: string;
};

export type CreatePayrollComponent = Omit<PayrollTemplate, "components"> & {
  components: PayrollTemplateComponent;
};
export type PayrollTemplateComponent = {
  id: string;
  name: string;
  sub_components: PayrollTemplateComponent[];
};
