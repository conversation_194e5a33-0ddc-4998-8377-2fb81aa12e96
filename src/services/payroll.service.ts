import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import { BaseResponse } from "./api_definitions/default.service";
import {
  CreatePayrollComponent,
  PayrollComponentV2,
  PayrollTemplate,
  PayrollTemplateV2,
} from "./api_definitions/payroll.service";

class PayrollService {
  getAllTemplates = async () => {
    const resp = await httpClient<BaseResponse<PayrollTemplate[]>>(apiRegister.PAYROLL.paths["get-payroll-templates"]);
    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return resp?.data?.response;
  };

  getAllTemplatesV2 = async () => {
    const resp = await httpClient<BaseResponse<PayrollTemplateV2[]>>(
      apiRegister.PAYROLL.paths["get-payroll-templates"],
    );
    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return resp?.data?.response;
  };

  createTemplate = async (template: PayrollTemplateV2) => {
    const resp = await httpClient<BaseResponse<CreatePayrollComponent>>(
      apiRegister.PAYROLL.paths["add-payroll-template"],
      {
        method: "POST",
        data: template,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error creating template");
    }
    return resp?.data?.response;
  };

  updateTemplate = async (template: PayrollTemplateV2) => {
    const resp = await httpClient<BaseResponse<CreatePayrollComponent>>(
      apiRegister.PAYROLL.paths["update-payroll-template"],
      {
        method: "PATCH",
        data: template,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error updating template");
    }
    return resp?.data?.response;
  };

  deleteTemplate = async (templateId: string) => {
    const resp = await httpClient<BaseResponse<CreatePayrollComponent>>(
      apiRegister.PAYROLL.paths["delete-payroll-template"],
      {
        method: "DELETE",
        data: {
          name: templateId,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error deleting template");
    }
    return resp?.data?.response;
  };

  getAllCompensationComponents = async (country: string) => {
    const resp = await httpClient<BaseResponse<PayrollComponentV2[]>>(
      apiRegister.PAYROLL.paths["all-payroll-components"],
      {
        params: {
          country,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching all compensation components");
    }
    return resp?.data?.response || [];
  };

  softDelete = async (mode: "activate" | "deactivate", name: string) => {
    const resp = await httpClient<BaseResponse<string>>(
      apiRegister.PAYROLL.paths["soft-delete"].replace(":mode", mode),
      {
        method: "PUT",
        data: {
          name,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error soft deleting template");
    }
    return resp?.data?.response;
  };
}

export default new PayrollService();
