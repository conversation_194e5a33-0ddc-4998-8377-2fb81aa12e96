import { JobTitle } from "src/services/api_definitions/performanceManagement.service";

class WorkRoleUtils {
  filterLevels = (band: string, levelOptions: { name: string; bandName: string }[]) => {
    return levelOptions.filter((level) => level.bandName === band);
  };

  // refactor this
  filterGrades = (
    bandName: string | null,
    level: string | null,
    gradeOptions: { name: string; levelName: string; bandName: string }[],
  ) => {
    return gradeOptions.filter((grade) => {
      if (bandName && level) {
        return grade.bandName === bandName && grade.levelName === level;
      } else if (level) {
        return grade.levelName === level;
      } else if (bandName) {
        return grade.bandName === bandName;
      }
      return true; // Return all if no filters are provided
    });
  };

  filterJobTitles = (
    bandName: string | null,
    levelName: string | null,
    gradeName: string | null,
    jobTitleOptions: JobTitle[],
  ) => {
    return jobTitleOptions.filter((jobTitle) => {
      if (bandName && levelName && gradeName) {
        return jobTitle.band === bandName && jobTitle.level === levelName && jobTitle.grade === gradeName;
      }

      if (bandName && levelName) {
        console.log({ jobTitleOptions, bandName, levelName, gradeName });
        return jobTitle.band === bandName && jobTitle.level === levelName;
      }

      if (bandName && gradeName) {
        return jobTitle.band === bandName && jobTitle.grade === gradeName;
      }

      if (levelName && gradeName) {
        return jobTitle.level === levelName && jobTitle.grade === gradeName;
      }
      return true;
    });
  };
}

export default new WorkRoleUtils();
